#!/usr/bin/env bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
  echo "Usage: bin/dev [COMMAND]"
  echo ""
  echo "Commands:"
  echo "  start, s          Start all services (default)"
  echo "  web, w            Start only Rails server"
  echo "  sidekiq, sq       Start only Sidekiq"
  echo "  webpack, wp       Start only Webpack dev server"
  echo "  debug, d          Start Rails server for debugging (no other processes)"
  echo "  help, h           Show this help"
  echo ""
  echo "Examples:"
  echo "  bin/dev           # Start all services"
  echo "  bin/dev web       # Start only Rails server"
  echo "  bin/dev debug     # Start Rails server for debugging"
}

start_all() {
  echo -e "${GREEN}Starting all development services...${NC}"
  if command -v foreman &> /dev/null; then
    exec foreman start -f Procfile.dev
  else
    echo -e "${YELLOW}For<PERSON> not found. Installing...${NC}"
    if command -v gem &> /dev/null; then
      gem install foreman
      echo -e "${GREEN}Foreman installed successfully!${NC}"
      exec foreman start -f Procfile.dev
    else
      echo -e "${RED}Ruby/gem not found. Please install Ruby first.${NC}"
      exit 1
    fi
  fi
}

start_web() {
  echo -e "${BLUE}Starting Rails server on port 4000...${NC}"
  exec rails s -p 4000 -u thin
}

start_sidekiq() {
  echo -e "${YELLOW}Starting Sidekiq...${NC}"
  exec sidekiq
}

start_webpack() {
  echo -e "${GREEN}Starting Webpack dev server...${NC}"
  exec bin/shakapacker dev-server
}

start_debug() {
  echo -e "${RED}Starting Rails server in debug mode...${NC}"
  echo -e "${YELLOW}You can now use 'binding.break', 'debugger', or 'byebug' in your code${NC}"
  exec rails s -p 4000 -u thin
}

# Parse command line arguments
case "${1:-start}" in
  start|s)
    start_all
    ;;
  web|w)
    start_web
    ;;
  sidekiq|sq)
    start_sidekiq
    ;;
  webpack|wp)
    start_webpack
    ;;
  debug|d)
    start_debug
    ;;
  help|h)
    show_help
    ;;
  *)
    echo -e "${RED}Unknown command: $1${NC}"
    echo ""
    show_help
    exit 1
    ;;
esac
